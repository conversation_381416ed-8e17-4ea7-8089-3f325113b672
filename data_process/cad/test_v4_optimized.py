#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的DXF解析器V4版本
主要测试：
1. 基于layout图像的精准版面识别
2. 整合版面识别与DXF解析结果的详细日志
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from dxf_parser_structured_v4_claude import DXFStructuredParserV4, json_serializable


def test_optimized_v4_parser():
    """测试优化后的V4解析器"""
    print("🚀 测试优化后的DXF解析器V4版本")
    print("=" * 60)
    
    # 测试文件路径 - 使用您提到的文件
    test_file = "BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf"
    
    # 查找测试文件
    possible_paths = [
        os.path.join(current_dir, test_file),
        os.path.join(current_dir, "..", "..", test_file),
        os.path.join("/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test", test_file)
    ]
    
    dxf_path = None
    for path in possible_paths:
        if os.path.exists(path):
            dxf_path = path
            break
    
    if not dxf_path:
        print(f"❌ 未找到测试文件: {test_file}")
        print("请确保文件存在于以下位置之一:")
        for path in possible_paths:
            print(f"   - {path}")
        return False
    
    print(f"📁 使用测试文件: {os.path.basename(dxf_path)}")
    print(f"📍 文件路径: {dxf_path}")
    
    try:
        # 创建优化后的V4解析器
        print("\n🔧 创建优化后的V4解析器...")
        parser = DXFStructuredParserV4(
            dxf_path,
            include_coordinates=True,
            include_raw_data=True
        )
        
        # 进行版面识别解析
        print("\n🔄 开始优化后的版面识别解析...")
        result = parser.analyze_layout_with_ocr()
        
        # 检查结果
        print("\n📊 解析结果分析:")
        print(f"   版面分析方法: {result.get('版面分析', {}).get('分析方法', '未知')}")
        print(f"   总页面数: {result.get('版面分析', {}).get('总页面数', 0)}")
        print(f"   图纸数量: {result.get('图纸结构', {}).get('图纸数量', 0)}")
        print(f"   表格数量: {len(result.get('表格数据', []))}")
        
        # 检查是否使用了layout图像
        layout_details = result.get('版面分析', {}).get('布局详情', [])
        for detail in layout_details:
            if isinstance(detail, dict) and 'layout_image_used' in str(detail):
                print("   ✅ 使用了layout图像进行精准识别")
                break
        else:
            print("   ⚠️  未使用layout图像")
        
        # 保存结果
        output_dir = os.path.dirname(dxf_path)
        base_name = os.path.splitext(os.path.basename(dxf_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}_v4_optimized.json")
        
        # 转换为JSON可序列化格式
        serializable_result = json_serializable(result)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 解析成功!")
        print(f"📄 结果已保存: {os.path.basename(output_file)}")
        
        # 检查生成的layout图像
        layout_image_name = f"{base_name}_layout.png"
        layout_image_path = os.path.join(output_dir, layout_image_name)
        if os.path.exists(layout_image_path):
            print(f"🖼️  Layout图像已生成: {layout_image_name}")
        else:
            print(f"⚠️  Layout图像未找到: {layout_image_name}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_optimized_v4_parser()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 优化后的V4解析器测试成功!")
        print("\n主要改进:")
        print("1. ✅ 使用layout图像进行精准版面识别")
        print("2. ✅ 增加详细的调试日志")
        print("3. ✅ 改进整合版面识别与DXF解析结果的流程")
    else:
        print("❌ 优化后的V4解析器测试失败!")
    
    print("\n请查看控制台输出中的详细日志信息。")


if __name__ == "__main__":
    main()
