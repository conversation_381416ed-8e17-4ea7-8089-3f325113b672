# DXF Parser V1.0 使用说明

## 概述

`dxf_parser_v1.py` 是对原有 `dxf_parser.py` 的优化升级版本，整合了 `dxf_deep_parser.py` 的深度解析能力，提供了更全面的DXF文件解析功能。

## 主要特性

### 1. 全面的内容解析
- 支持所有常见的DXF实体类型（LINE、CIRCLE、ARC、TEXT、MTEXT、INSERT等）
- 深度提取文本内容，包括模型空间、图纸空间和块定义中的文本
- 提取完整的几何信息和属性信息
- 支持多种文本实体的清理和格式化

### 2. 保留原有输出格式
- 使用中文键名，对大模型友好
- 保持与原 `dxf_parser.py` 相同的JSON结构
- 包含文件元数据、文本内容汇总、图层信息和实体列表

### 3. 批量处理能力
- 支持单文件处理
- 支持目录批量处理（递归查找所有DXF文件）
- 输出文件名为 `原文件名.json`
- 可指定输出目录

### 4. 增强的功能
- 详细的进度显示（使用tqdm）
- 完善的错误处理和异常捕获
- 统计信息和处理结果报告
- 命令行参数支持

## 使用方法

### 基本用法

```bash
# 处理单个DXF文件（输出到同目录）
python dxf_parser_v1.py input.dxf

# 批量处理目录中的所有DXF文件
python dxf_parser_v1.py /path/to/dxf/directory

# 指定输出目录
python dxf_parser_v1.py input.dxf -o /path/to/output
python dxf_parser_v1.py /path/to/dxf/directory -o /path/to/output
```

### 命令行参数

- `input`: 输入DXF文件或包含DXF文件的目录（必需）
- `-o, --output`: 输出目录（可选，默认为输入文件同目录）
- `--version`: 显示版本信息

### 使用示例

```bash
# 示例1：处理单个文件
python dxf_parser_v1.py "火灾自动报警系统配线图.dxf"

# 示例2：批量处理并指定输出目录
python dxf_parser_v1.py "/Users/<USER>/Downloads/dxf_files" -o "/Users/<USER>/output"

# 示例3：查看帮助信息
python dxf_parser_v1.py -h
```

## 输出格式

生成的JSON文件包含以下主要部分：

### 1. 文件元数据
```json
{
    "文件元数据": {
        "文件名": "input.dxf",
        "DXF版本": "AC1027",
        "单位": "未指定 (通常为毫米)",
        "坐标系": "世界坐标系 (WCS)",
        "实体统计": {...},
        "文本实体数量": 1341,
        "图层数量": 15
    }
}
```

### 2. 文本内容汇总
```json
{
    "文本内容汇总": {
        "所有文本实体": [...],
        "按图层分组": {...},
        "所有文本内容": [...],
        "文本实体数量": 1341
    }
}
```

### 3. 图层信息
```json
{
    "图层信息": {
        "图层名": {
            "颜色": 7,
            "线型": "Continuous",
            "是否冻结": false,
            "是否锁定": false,
            "是否关闭": false
        }
    }
}
```

### 4. 实体列表
```json
{
    "实体列表": [
        {
            "实体类型": "LINE",
            "所在图层": "0",
            "颜色索引": 256,
            "几何信息": {
                "起点坐标": [0, 0, 0],
                "终点坐标": [100, 100, 0]
            }
        }
    ]
}
```

## 支持的实体类型

- **几何实体**: LINE, CIRCLE, ARC, ELLIPSE, LWPOLYLINE, POLYLINE, SPLINE, POINT, SOLID
- **文本实体**: TEXT, MTEXT, ATTRIB, ATTDEF
- **复合实体**: INSERT (块插入), DIMENSION (尺寸标注), LEADER (引线)
- **填充实体**: HATCH
- **其他实体**: 自动识别并提取基本属性

## 性能特点

- 处理速度快：单个文件通常在几秒内完成
- 内存效率高：流式处理，适合大文件
- 错误恢复：单个文件失败不影响批量处理
- 进度显示：实时显示处理进度

## 与原版本的对比

| 特性 | dxf_parser.py | dxf_parser_v1.py |
|------|---------------|------------------|
| 实体类型支持 | 基本类型 | 全面支持 |
| 文本提取 | 模型空间 | 全空间（模型+图纸+块） |
| 批量处理 | 不支持 | 支持 |
| 进度显示 | 无 | 有 |
| 错误处理 | 基本 | 完善 |
| 命令行支持 | 无 | 有 |
| 输出格式 | 中文键名 | 保持兼容 |

## 依赖要求

```bash
pip install ezdxf tqdm
```

## 注意事项

1. 确保输入路径存在且可访问
2. 输出目录会自动创建（如果不存在）
3. 同名文件会被覆盖
4. 大文件处理可能需要较长时间
5. 损坏的DXF文件会被跳过并记录在失败列表中

## 故障排除

### 常见问题

1. **"无法找到或打开文件"**
   - 检查文件路径是否正确
   - 确认文件存在且有读取权限

2. **"无效或损坏的DXF文件"**
   - 使用CAD软件检查文件完整性
   - 尝试重新保存DXF文件

3. **"处理异常"**
   - 查看详细错误信息
   - 检查文件是否被其他程序占用

### 调试模式

如果遇到问题，可以修改脚本中的错误处理部分，添加更详细的调试信息。

## 更新日志

### v1.0 (2025-07-31)
- 初始版本发布
- 整合原有功能和深度解析能力
- 添加批量处理支持
- 完善错误处理和用户体验
