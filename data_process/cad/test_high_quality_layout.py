#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用高质量PDF进行版面布局识别测试
直接使用已生成的高质量PDF文件
"""

import os
import sys
import cv2
import json
import numpy as np
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_with_high_quality_pdf():
    """使用高质量PDF进行版面分析"""
    print("🔍 使用高质量PDF进行版面布局识别")
    print("=" * 60)
    
    # 使用已生成的高质量PDF (ezdxf渲染)
    high_quality_pdf = "/Users/<USER>/PycharmProjects/crawl/pdf_test_output/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10_ezdxf_drawing.pdf"
    
    if not os.path.exists(high_quality_pdf):
        print(f"❌ 高质量PDF文件不存在: {high_quality_pdf}")
        return None
    
    file_size = os.path.getsize(high_quality_pdf) / 1024  # KB
    print(f"📁 使用高质量PDF: {os.path.basename(high_quality_pdf)}")
    print(f"📊 文件大小: {file_size:.1f}KB")
    
    try:
        from dxf_parser_structured_v4 import DXFLayoutAnalyzer
        
        analyzer = DXFLayoutAnalyzer()
        
        # 1. 直接从PDF转图像
        print("\n1️⃣ 转换PDF到图像...")
        images = analyzer.pdf_to_image(high_quality_pdf)
        if not images:
            print("❌ 图像转换失败")
            return None
        
        print(f"✅ 成功转换 {len(images)} 页图像")
        for i, image in enumerate(images):
            print(f"   页面{i+1}: {image.shape}")
        
        # 2. 进行版面布局分析
        print("\n2️⃣ 进行版面布局分析...")
        layout_results = []
        
        for i, image in enumerate(images):
            print(f"   分析页面{i+1}...")
            layout_result = analyzer.analyze_layout(image)
            layout_results.append({
                "page": i + 1,
                "layout": layout_result
            })
        
        # 3. 保存结果 (处理序列化问题)
        output_file = './high_quality_layout_result.json'

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(layout_results, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"⚠️  JSON保存失败: {e}")
            # 使用pickle作为备用
            import pickle
            pickle_file = output_file.replace('.json', '.pkl')
            with open(pickle_file, 'wb') as f:
                pickle.dump(layout_results, f)
            print(f"✅ 结果已保存为pickle格式: {pickle_file}")
        
        print(f"✅ 分析完成，结果已保存到: {output_file}")
        
        # 4. 显示分析结果
        print(f"\n📊 版面分析结果:")
        total_pages = 0
        for page_result in layout_results:
            layout = page_result["layout"]
            if "pages" in layout:
                total_pages += layout["total_pages"]
                print(f"   PDF页面{page_result['page']}: 检测到 {layout['total_pages']} 个图纸页面")
                
                for page in layout["pages"]:
                    print(f"     图纸页面{page['page_number']}:")
                    print(f"       主图区域: {'有' if page.get('main_drawing') else '无'}")
                    print(f"       表格区域: {len(page.get('tables', []))}个")
                    print(f"       图例区域: {len(page.get('legends', []))}个")
                    
                    # 显示表格详情
                    for j, table in enumerate(page.get('tables', [])):
                        print(f"         表格{j+1}: {table['position']} - {table['width']}x{table['height']}")
        
        print(f"\n🎯 总结: 检测到 {total_pages} 个图纸页面")
        
        # 5. 生成可视化
        visualize_high_quality_layout(images, layout_results)
        
        return layout_results
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def visualize_high_quality_layout(images, layout_results):
    """可视化高质量PDF的版面检测结果"""
    print(f"\n🎨 生成可视化结果...")
    
    try:
        for i, (image, page_result) in enumerate(zip(images, layout_results)):
            layout = page_result["layout"]
            
            if "pages" in layout:
                # 创建可视化图像
                vis_image = image.copy()
                
                for page_idx, page in enumerate(layout["pages"]):
                    print(f"   可视化页面{page['page_number']}...")
                    
                    # 绘制页面边界 (蓝色)
                    page_bounds = page["page_bounds"]
                    cv2.rectangle(vis_image, 
                                (page_bounds["x"], page_bounds["y"]),
                                (page_bounds["x"] + page_bounds["width"], 
                                 page_bounds["y"] + page_bounds["height"]),
                                (255, 0, 0), 3)
                    
                    # 添加页面标签
                    cv2.putText(vis_image, f"Page {page['page_number']}", 
                              (page_bounds["x"], page_bounds["y"] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 0, 0), 2)
                    
                    # 绘制主图区域 (绿色)
                    if page.get("main_drawing"):
                        main_drawing = page["main_drawing"]
                        cv2.rectangle(vis_image,
                                    (main_drawing["x"], main_drawing["y"]),
                                    (main_drawing["x"] + main_drawing["width"],
                                     main_drawing["y"] + main_drawing["height"]),
                                    (0, 255, 0), 2)
                        
                        cv2.putText(vis_image, "Main Drawing", 
                                  (main_drawing["x"], main_drawing["y"] - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                    
                    # 绘制表格区域 (红色)
                    for j, table in enumerate(page.get("tables", [])):
                        cv2.rectangle(vis_image,
                                    (table["x"], table["y"]),
                                    (table["x"] + table["width"],
                                     table["y"] + table["height"]),
                                    (0, 0, 255), 2)
                        
                        cv2.putText(vis_image, f"Table {j+1} ({table['position']})", 
                                  (table["x"], table["y"] - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                    
                    # 绘制图例区域 (紫色)
                    for j, legend in enumerate(page.get("legends", [])):
                        cv2.rectangle(vis_image,
                                    (legend["x"], legend["y"]),
                                    (legend["x"] + legend["width"],
                                     legend["y"] + legend["height"]),
                                    (255, 0, 255), 2)
                        
                        cv2.putText(vis_image, f"Legend {j+1}", 
                                  (legend["x"], legend["y"] - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)
                
                # 保存可视化结果
                vis_output = f"high_quality_layout_vis_page_{i+1}.png"
                cv2.imwrite(vis_output, vis_image)
                print(f"   ✅ 可视化结果已保存: {vis_output}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        return False

def compare_pdf_quality():
    """对比不同质量PDF的效果"""
    print(f"\n🔄 对比不同质量PDF的版面识别效果")
    print("-" * 50)
    
    pdfs = [
        {
            "name": "高质量PDF (ezdxf渲染)",
            "path": "/Users/<USER>/PycharmProjects/crawl/pdf_test_output/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10_ezdxf_drawing.pdf"
        },
        {
            "name": "基础PDF (matplotlib渲染)",
            "path": "/Users/<USER>/PycharmProjects/crawl/pdf_test_output/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10_custom_matplotlib.pdf"
        },
        {
            "name": "V4优化PDF",
            "path": "/Users/<USER>/PycharmProjects/crawl/pdf_test_output/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10_v4_optimized.pdf"
        }
    ]
    
    for pdf_info in pdfs:
        if os.path.exists(pdf_info["path"]):
            file_size = os.path.getsize(pdf_info["path"]) / 1024
            print(f"📁 {pdf_info['name']}: {file_size:.1f}KB")
        else:
            print(f"❌ {pdf_info['name']}: 文件不存在")

def main():
    """主函数"""
    print("🚀 高质量PDF版面布局识别测试")
    print("=" * 50)
    
    # 1. 对比PDF质量
    compare_pdf_quality()
    
    # 2. 使用高质量PDF进行版面分析
    result = test_with_high_quality_pdf()
    
    if result:
        print(f"\n🎉 测试完成!")
        print(f"生成的文件:")
        print(f"  - high_quality_layout_result.json: 详细分析结果")
        print(f"  - high_quality_layout_vis_page_*.png: 可视化结果")
        
        print(f"\n💡 建议:")
        print(f"  - 高质量PDF能够提供更好的版面识别效果")
        print(f"  - 建议在生产环境中使用ezdxf渲染器")
    else:
        print(f"\n❌ 测试失败")

if __name__ == "__main__":
    main()
