#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的版面布局识别功能
专注于双页布局、主图区域、表格区域的识别
"""

import os
import sys
import cv2
import json
import numpy as np
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_layout_detection(dxf_path: str):
    """测试版面布局识别"""
    print(f"🔍 测试版面布局识别")
    print(f"📁 测试文件: {os.path.basename(dxf_path)}")
    print("=" * 60)
    
    try:
        from dxf_parser_structured_v4 import DXFStructuredParserV4
        
        # 创建解析器
        parser = DXFStructuredParserV4(dxf_path, include_coordinates=True, include_raw_data=True)
        
        # 执行完整的版面识别解析流程
        result = parser.analyze_layout_with_ocr()
        
        # 保存结果
        output_file = './layout_detection_result.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 分析完成，结果已保存到: {output_file}")
        
        # 显示布局识别结果
        if "版面分析" in result:
            layout_info = result["版面分析"]
            print(f"\n📊 版面分析结果:")
            print(f"   分析方法: {layout_info.get('分析方法', '未知')}")
            print(f"   总页面数: {layout_info.get('总页面数', 0)}")
            
            if "布局详情" in layout_info:
                print(f"\n📄 布局详情:")
                for i, page in enumerate(layout_info["布局详情"]):
                    print(f"   图纸页面 {page['图纸页面']}:")
                    print(f"     📐 主图区域: {'有' if page.get('主图区域') else '无'}")
                    if page.get('主图区域'):
                        main_drawing = page['主图区域']
                        print(f"        位置: ({main_drawing['x']}, {main_drawing['y']})")
                        print(f"        尺寸: {main_drawing['width']} x {main_drawing['height']}")
                    
                    print(f"     📋 表格区域: {len(page.get('表格区域', []))}个")
                    for j, table in enumerate(page.get('表格区域', [])):
                        print(f"        表格{j+1}: {table['position']} - 尺寸: {table['width']} x {table['height']}")
                    
                    print(f"     🎨 图例区域: {len(page.get('图例区域', []))}个")
                    print()
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def visualize_layout_detection(dxf_path: str):
    """可视化版面检测结果"""
    print(f"\n🎨 可视化版面检测结果")
    print("-" * 40)
    
    try:
        from dxf_parser_structured_v4 import DXFLayoutAnalyzer
        
        analyzer = DXFLayoutAnalyzer()
        
        # 1. DXF转PDF
        print("1️⃣ 转换DXF到PDF...")
        pdf_path = analyzer.dxf_to_pdf(dxf_path)
        if not pdf_path:
            print("❌ PDF转换失败")
            return
        
        # 2. PDF转图像
        print("2️⃣ 转换PDF到图像...")
        images = analyzer.pdf_to_image(pdf_path)
        if not images:
            print("❌ 图像转换失败")
            return
        
        # 3. 对每个图像进行版面分析并可视化
        for i, image in enumerate(images):
            print(f"\n3️⃣ 分析第{i+1}页...")
            
            # 进行版面分析
            layout_result = analyzer.analyze_layout(image)
            
            if "pages" in layout_result:
                # 创建可视化图像
                vis_image = image.copy()
                
                for page_idx, page in enumerate(layout_result["pages"]):
                    print(f"   页面{page['page_number']}:")
                    
                    # 绘制页面边界
                    page_bounds = page["page_bounds"]
                    cv2.rectangle(vis_image, 
                                (page_bounds["x"], page_bounds["y"]),
                                (page_bounds["x"] + page_bounds["width"], 
                                 page_bounds["y"] + page_bounds["height"]),
                                (255, 0, 0), 3)  # 蓝色边框
                    
                    # 绘制主图区域
                    if page.get("main_drawing"):
                        main_drawing = page["main_drawing"]
                        cv2.rectangle(vis_image,
                                    (main_drawing["x"], main_drawing["y"]),
                                    (main_drawing["x"] + main_drawing["width"],
                                     main_drawing["y"] + main_drawing["height"]),
                                    (0, 255, 0), 2)  # 绿色边框
                        
                        # 添加标签
                        cv2.putText(vis_image, "Main Drawing", 
                                  (main_drawing["x"], main_drawing["y"] - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        
                        print(f"     主图区域: {main_drawing['width']} x {main_drawing['height']}")
                    
                    # 绘制表格区域
                    for j, table in enumerate(page.get("tables", [])):
                        cv2.rectangle(vis_image,
                                    (table["x"], table["y"]),
                                    (table["x"] + table["width"],
                                     table["y"] + table["height"]),
                                    (0, 0, 255), 2)  # 红色边框
                        
                        # 添加标签
                        cv2.putText(vis_image, f"Table {j+1} ({table['position']})", 
                                  (table["x"], table["y"] - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                        
                        print(f"     表格{j+1}: {table['position']} - {table['width']} x {table['height']}")
                
                # 保存可视化结果
                vis_output = f"layout_visualization_page_{i+1}.png"
                cv2.imwrite(vis_output, vis_image)
                print(f"   ✅ 可视化结果已保存: {vis_output}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_original_ocr(dxf_path: str):
    """与原始OCR方法对比"""
    print(f"\n🔄 对比测试: 新布局识别 vs 原始OCR")
    print("-" * 50)
    
    # 这里可以添加与原始OCR方法的对比逻辑
    # 暂时跳过详细实现
    print("⚠️  对比功能待完善")

def main():
    """主函数"""
    print("🚀 版面布局识别测试工具")
    print("=" * 50)
    
    # 使用测试文件
    dxf_path = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf"
    
    if not os.path.exists(dxf_path):
        print(f"❌ 测试文件不存在: {dxf_path}")
        return
    
    # 1. 测试版面布局识别
    result = test_layout_detection(dxf_path)
    
    if result:
        # 2. 可视化检测结果
        visualize_layout_detection(dxf_path)
        
        # 3. 对比测试
        compare_with_original_ocr(dxf_path)
        
        print(f"\n🎉 测试完成!")
        print(f"生成的文件:")
        print(f"  - layout_detection_result.json: 详细分析结果")
        print(f"  - layout_visualization_page_*.png: 可视化结果")
    else:
        print(f"\n❌ 测试失败")

if __name__ == "__main__":
    main()
